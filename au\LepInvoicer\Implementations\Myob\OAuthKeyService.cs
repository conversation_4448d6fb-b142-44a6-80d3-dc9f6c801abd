using MYOB.AccountRight.SDK;
using MYOB.AccountRight.SDK.Contracts;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;

namespace LepInvoicer.Implementations.Myob;

/// <summary>
/// OAuth key service for persisting MYOB OAuth tokens with automatic refresh capability
/// </summary>
public class OAuthKeyService : MYOB.AccountRight.SDK.IOAuthKeyService, Interfaces.IOAuthKeyService
{
	private readonly string _tokensFile;
	private readonly ILogger<OAuthKeyService> _logger;
	private OAuthTokens _tokens;
	private ApiConfiguration _apiConfiguration;

	public OAuthKeyService(ILogger<OAuthKeyService> logger)
	{
		_logger = logger;

		// Set tokens file path relative to the application directory
		var appDirectory = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location)
						  ?? Environment.CurrentDirectory;
		_tokensFile = Path.Combine(appDirectory, "Tokens.json");

		_logger.LogDebug("OAuth tokens file path: {TokensFile}", _tokensFile);
		ReadFromFile();
	}

	/// <summary>
	/// Constructor with API configuration for token refresh capability
	/// </summary>
	public OAuthKeyService(ILogger<OAuthKeyService> logger, ApiConfiguration apiConfiguration) : this(logger)
	{
		_apiConfiguration = apiConfiguration;
		_logger.LogDebug("OAuth key service initialized with API configuration for automatic token refresh");
	}

	/// <summary>
	/// OAuth response property that holds the tokens with automatic refresh
	/// </summary>
	public OAuthTokens OAuthResponse
	{
		get
		{
			// Check if tokens need refreshing before returning
			if (_tokens != null && IsTokenExpired(_tokens) && _apiConfiguration != null)
			{
				_logger.LogInformation("Access token expired, attempting automatic refresh");
				RefreshTokensIfNeeded();
			}
			return _tokens;
		}
		set
		{
			_tokens = value;
			SaveToFile();
		}
	}



	/// <summary>
	/// Read tokens from file
	/// </summary>
	private void ReadFromFile()
	{
		try
		{
			if (!File.Exists(_tokensFile))
			{
				_logger.LogInformation("OAuth tokens file not found, will create new one at: {TokensFile}", _tokensFile);
				_tokens = null;
				return;
			}

			var json = File.ReadAllText(_tokensFile);
			_tokens = JsonConvert.DeserializeObject<OAuthTokens>(json);
			_logger.LogInformation("OAuth tokens loaded from file: {TokensFile}", _tokensFile);
		}
		catch (FileNotFoundException)
		{
			_logger.LogInformation("OAuth tokens file not found, will create new one");
			_tokens = null;
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Failed to read OAuth tokens from file");
			_tokens = null;
		}
	}

	/// <summary>
	/// Save tokens to file
	/// </summary>
	private void SaveToFile()
	{
		try
		{
			// Ensure directory exists
			var directory = Path.GetDirectoryName(_tokensFile);
			if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
			{
				Directory.CreateDirectory(directory);
				_logger.LogInformation("Created directory for tokens file: {Directory}", directory);
			}

			var json = JsonConvert.SerializeObject(_tokens, Formatting.Indented);
			File.WriteAllText(_tokensFile, json);
			_logger.LogInformation("OAuth tokens saved to file: {TokensFile}", _tokensFile);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Failed to save OAuth tokens to file");
		}
	}

	/// <summary>
	/// Check if the access token is expired or will expire soon
	/// </summary>
	private bool IsTokenExpired(OAuthTokens tokens)
	{
		if (tokens == null) return true;

		// Check the HasExpired property first
		if (tokens.HasExpired) return true;

		// Use reflection to get the properties since the MYOB SDK might use different naming
		try
		{
			var tokenType = tokens.GetType();
			var receivedTimeProperty = tokenType.GetProperty("ReceivedTime");
			var expiresInProperty = tokenType.GetProperty("expires_in") ?? tokenType.GetProperty("ExpiresIn");

			if (receivedTimeProperty != null && expiresInProperty != null)
			{
				var receivedTime = (DateTime?)receivedTimeProperty.GetValue(tokens);
				var expiresIn = expiresInProperty.GetValue(tokens);

				if (receivedTime.HasValue && expiresIn != null)
				{
					var expiresInSeconds = Convert.ToInt32(expiresIn);
					var expirationTime = receivedTime.Value.AddSeconds(expiresInSeconds);
					var bufferTime = TimeSpan.FromMinutes(5); // Refresh 5 minutes before expiration

					var isExpired = DateTime.UtcNow >= expirationTime.Subtract(bufferTime);

					if (isExpired)
					{
						_logger.LogDebug("Token expired: ReceivedTime={ReceivedTime}, ExpiresIn={ExpiresIn}s, ExpirationTime={ExpirationTime}, CurrentTime={CurrentTime}",
							receivedTime, expiresInSeconds, expirationTime, DateTime.UtcNow);
					}

					return isExpired;
				}
			}
		}
		catch (Exception ex)
		{
			_logger.LogWarning(ex, "Error checking token expiration using reflection");
		}

		// If we can't determine expiration, assume it's expired to be safe
		_logger.LogWarning("Cannot determine token expiration - missing ReceivedTime or expires_in properties");
		return true;
	}

	/// <summary>
	/// Refresh tokens using the refresh token
	/// </summary>
	private void RefreshTokensIfNeeded()
	{
		// Use reflection to get the refresh token since property names might vary
		string refreshToken = null;
		try
		{
			var tokenType = _tokens?.GetType();
			var refreshTokenProperty = tokenType?.GetProperty("refresh_token") ?? tokenType?.GetProperty("RefreshToken");
			if (refreshTokenProperty != null)
			{
				refreshToken = refreshTokenProperty.GetValue(_tokens) as string;
			}
		}
		catch (Exception ex)
		{
			_logger.LogWarning(ex, "Error getting refresh token using reflection");
		}

		if (string.IsNullOrEmpty(refreshToken))
		{
			_logger.LogWarning("Cannot refresh tokens - no refresh token available");
			return;
		}

		if (_apiConfiguration == null)
		{
			_logger.LogWarning("Cannot refresh tokens - no API configuration available");
			return;
		}

		try
		{
			_logger.LogInformation("Refreshing OAuth tokens using refresh token");

			var oauthService = new OAuthService(_apiConfiguration);
			var newTokens = oauthService.RenewTokens(_tokens);

			if (newTokens != null)
			{
				_tokens = newTokens;
				SaveToFile();
				_logger.LogInformation("OAuth tokens refreshed successfully");
			}
			else
			{
				_logger.LogError("Failed to refresh tokens - received null response");
			}
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Failed to refresh OAuth tokens");
			// Don't throw here - let the calling code handle the expired token
		}
	}
}
