using lep.job;
using RoundedRectangles;
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Printing;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Reflection;
using lep.extensionmethods;
using Serilog;
using NHibernate.Criterion;

namespace lep.despatch.impl.label
{
	public class DPCPocessingThumbnailLabel : BaseFurtherProcessingLabel
	{
		#region Constructors

		public DPCPocessingThumbnailLabel()
		{
		}

		#endregion Constructors



		#region Protected Methods

		protected override void OnPrintPage(PrintPageEventArgs e)
		{
			base.OnPrintPage(e);
			var g = e.Graphics;

			// Set graphics unit to millimeters - this is the key!
			g.PageUnit = GraphicsUnit.Millimeter;

			SetupGraphics(g);

			// UNCOMMENT NEXT LINE TO SHOW 5MM GRID FOR FINE-TUNING
			DrawGrid(g);

			pageNumber++;
			if (pageNumber == 1)
			{
				#region title

				var title = "DPC :" + Job.Template.Name;
				g.DrawString(title, titleFont, Brushes.Black, rTitle, titleAlignment);

				#endregion title

				#region print DPC processing instruction in top part

				var strMiddle = processingText1.ToString();
				var hMiddle = (int)g.MeasureString(strMiddle, this.defaultFont).Height;
				var rMiddle = new RectangleF(2f, 15f, 99f, 50f); // Left side processing text

				g.DrawString(strMiddle, defaultFont, Brushes.Black, rMiddle, middleFormat);

				var strRight = processingText2.ToString();
				var hRight = (int)g.MeasureString(strRight, this.defaultFont).Height;
				var rright = new RectangleF(52f, 15f, 49f, 50f); // Right side processing text

				g.DrawString(strRight, defaultFont, Brushes.Black, rright, rightFormat);

				#endregion print DPC processing instruction in top part

				#region draw basic info like cust, job, order

				var basicJobInformation = basicInformation.ToString();

				//Rectangle rBasicInfo = new Rectangle(40, 284, 310, 100);
				//g.DrawRectangle(penBlack3, rBasicInfo);

				var hBasicInfo = (int)g.MeasureString(basicJobInformation, defaultFont).Height;

				g.DrawString(basicJobInformation, defaultFont, Brushes.Black, rBasicInfoTxt, middleFormat);

				#endregion draw basic info like cust, job, order

				DrawEDD(g);
				DrawRoundedCorndersBox(g);

				DrawThumbnail(g);
				DrawBarcode(g);
			}

			DrawSpecialInstructions(e, g);

			if (pageNumber == 2)
			{
				e.HasMorePages = false;
			}
		}

		#endregion Protected Methods

		#region Private Methods

		public override void FormatPrintContent()
		{
			try
			{
				// Clear StringBuilders
				processingText1.Clear();
				processingText2.Clear();

				// Strings to Print from Job Proerties
				FormatBasicInformation();

				processingText1.AppendFormat(fmt, "Job # ", Job.JobNr).AppendLine();
				processingText1.AppendFormat(fmt, "Stock", Job.FinalStock.Name).AppendLine();
				if (Job.IsMagazine())
				{
					if (Job.StockForCover != null)
					{
						processingText1.AppendFormat(fmt, "Cover stock:", Job.FinalStockForCover.Name).AppendLine();
					}
					processingText1.AppendFormat(fmt, "# of pages:", Job.Pages).AppendLine();

					var binding = Job.BindingOption?.Name ?? "";
					if (job.BoundEdge != JobBoundEdgeOptions.None)
					{
						binding += " " + job.BoundEdge.ToDescription().Substring(0, 1);
					}
					processingText1.AppendFormat(fmt, "Binding: ", binding).AppendLine();
				}

				processingText1.AppendFormat(fmt, "Size", Job.GetJobSize()).AppendLine();
				processingText1.AppendFormat(fmt, "Quantity", Job.Quantity).AppendLine();

				if (Job.Scoring)
				{
					processingText1.AppendFormat(fmt, "Scoring", "Yes").AppendLine();
				}
				//else
				//{
				//    processingText1.Append("Scoring :No").AppendLine();
				//}

				if (Job.Perforating)
				{
					processingText1.AppendFormat(fmt, "Perforation", "Yes").AppendLine();
				}
				//else
				//{
				//    processingText1.Append("Perforation :No").AppendLine();
				//}


				//else
				//{
				//    processingText1.Append("Magnet : No").AppendLine();
				//}

				//if (Job.FinishedSize.PaperSize.Name.Contains("Business Card"))
				//{
				//    processingText2.Append("Trim size :" +
				//    Job.FinishedSize.PaperSize.Name.Replace("Business Card", "B/C")).AppendLine();
				//}
				//else
				//{
				//    processingText2.Append("Trim size :" + Job.FinishedSize.PaperSize.Name).AppendLine();
				//}

				if (!Job.IsBusinessCard())
				{
					if (Job.FoldedSize != null)
					{
						processingText1.AppendFormat(fmt, "Fold", Job.FoldedSize.PaperSize.Name).AppendLine();
					}
				}
				else
				{
					if (Job.FoldedSize != null)
					{
						var size = Job.FoldedSize.Height.ToString() + "x" + Job.FoldedSize.Width.ToString();
						var title = Job.IsBusinessCard() ? "Scoring" : "Finish size";
						processingText1.AppendFormat(fmt, title, size).AppendLine();
					}
				}

				//if (Job.FinishedSize.PaperSize.Name == "Custom")


				if ((Job.FinalFrontCelloglaze == JobCelloglazeOptions.Gloss) &&
					(Job.FinalBackCelloglaze == JobCelloglazeOptions.Matt))
				{
					processingText1.AppendFormat(fmt, "Cello", "GlossFr/MattBk");
				}
				else
				{
					processingText1.AppendFormat(fmt, "Cello", Job.Celloglaze).AppendLine();
				}

				if (!Job.IsBrochure() || Job.Template.Is(JobTypeOptions.TentCalendars, JobTypeOptions.DLCalendars))
					processingText1.AppendFormat(fmt, "Diecut", Job.DieCutType.ToDescription()).AppendLine();

				if (Job.NumberOfMagnets > 0)
				{
					processingText1.AppendFormat(fmt, "Magnet", Job.NumberOfMagnets).AppendLine();
				}

				if (Job.HoleDrilling != HoleDrilling.None)
				{
					processingText1.AppendFormat(fmt, "Hole Drilling", Job.HoleDrilling.ToDescription()).AppendLine();

					if (Job.NumberOfHoles != null && Job.NumberOfHoles > 0)
					{
						processingText1.AppendFormat(fmt, "# Of Holes", Job.NumberOfHoles).AppendLine();
					}
				}

				if (Job.RoundOption != RoundOption.None)
				{
					processingText1.AppendFormat(fmt, "Round Corner", Job.RoundOption.ToDescription()).AppendLine();

					if (Job.RoundOption == RoundOption.Custom)
					{
						processingText1.AppendLine(Job.CustomDieCut);
					}
					else if (Job.RoundDetailOption != RoundDetailOption.None)
					{
						processingText1.AppendLine(Job.RoundDetailOption.ToDescription());
					}
				}

				if (Job.PadDirection != PadDirection.None)
				{
					processingText1.AppendFormat(fmt, "Finish by", Job.PadDirection.ToDescription()).AppendLine();
				}




				processingText1.AppendLine("Printed____\nCello____\nPacked____ ");

				FormatCommonSpecialInstructions();

				// Add DPC-specific special instructions
				try
				{
					if ( Job.IsWiroMagazine() &&  Job.WiroInfo != null)
					{
						strInstructions +=
						$@"Wiro Info:
						Covers
							Outer Front: {Job.WiroInfo.OuterFront ?? "None"}
							Outer Back:  {Job.WiroInfo.OuterBack ?? "None"}

							Inner Front
							Stock: {Job.WiroInfo.InnerFrontStockForCover?.Name ?? "None"}
							Cello: {Job.WiroInfo.InnerFrontCello ?? "None"}

							Inner Back
							Stock: {Job.WiroInfo.InnerBackStockForCover?.Name ?? "None"}
							Cello: {Job.WiroInfo.InnerBackCello ?? "None"}

						Wire Color:     {Job.WiroInfo.WireColor}
						Coil Thickness: {Job.WiroInfo.CoilThickness}
						Bound Edge:     {Job.BoundEdge.ToDescription()}
                        ";
					}
				}
				catch (Exception ex)
				{
					Log.Error(ex.Message, ex);
				}

				string t = Job.GetDigitalJobMailHouseInstuctions();
				strInstructions = strInstructions + t;

				if (Job.Order.PackWithoutPallets)
				{
					strInstructions += "\n***Pack Loose Cartons***";
				}



			}
			catch (Exception ex)
			{
				Log.Error(ex.Message, ex);
				throw;
			}
		}


		#endregion Private Methods
	}
}
