using lep.configuration;
using lep.extensionmethods;
using lep.job;
using System;
using System.Linq;
using System.Text;
using Serilog;

namespace lep.despatch.impl.label
{
    /// <summary>
    /// DTO containing all the information extracted from a job for Further Processing labels
    /// </summary>
    public class FurtherProcessingLabelData
    {
        // Basic Information
        public string JobName { get; set; }
        public string OrderNumber { get; set; }
        public string JobNumber { get; set; }
        public string OriginalJobNumber { get; set; }
        public string Courier { get; set; }
        public bool IsMultiJobOrder { get; set; }
        public string Barcode { get; set; }

        // Processing Text - Common
        public string RunNumber { get; set; }
        public string Size { get; set; }
        public int Quantity { get; set; }
        public string Stock { get; set; }
        public string CoverStock { get; set; }
        public string FrontPrinting { get; set; }
        public string BackPrinting { get; set; }
        public string Celloglaze { get; set; }
        public string FinalFrontCelloglaze { get; set; }
        public string FinalBackCelloglaze { get; set; }

        // Processing Text - Finishing
        public string HoleDrilling { get; set; }
        public int? NumberOfHoles { get; set; }
        public string RoundOption { get; set; }
        public string RoundDetailOption { get; set; }
        public string CustomDieCut { get; set; }
        public string DieCutType { get; set; }
        public string FoldedSize { get; set; }
        public string FoldingTitle { get; set; } // "Scoring" for business cards, "Folding" for others
        public int NumberOfMagnets { get; set; }
        public string PadDirection { get; set; }
        public bool Scoring { get; set; }
        public bool Perforating { get; set; }

        // Magazine specific
        public int Pages { get; set; }
        public string BindingOption { get; set; }
        public string BoundEdge { get; set; }

        // Special Instructions
        public bool SendSamples { get; set; }
        public bool CustomerLogoRequired { get; set; }
        public string SpecialInstructions { get; set; }
        public string DigitalJobMailHouseInstructions { get; set; }
        public bool PackWithoutPallets { get; set; }
        public string WiroInfo { get; set; }
        public string FreightPackages { get; set; }

        // Label Type Specific
        public LabelType LabelType { get; set; }
        public string Title { get; set; }
        public bool ShowPrintedCelloPacked { get; set; }

        // Formatted Output
        public string BasicInformationText { get; set; }
        public string ProcessingText1 { get; set; }
        public string ProcessingText2 { get; set; }
        public string SpecialInstructionsText { get; set; }
    }

    /// <summary>
    /// Utility class to extract all job information used across different Further Processing label types
    /// </summary>
    public static class FurtherProcessingLabelDataExtractor
    {
        private const string fmt = "{0,-8}: {1}";

        /// <summary>
        /// Extracts all job information and returns a consolidated DTO
        /// </summary>
        /// <param name="job">The job to extract information from</param>
        /// <param name="labelType">The type of label being generated</param>
        /// <returns>Consolidated job information DTO</returns>
        public static FurtherProcessingLabelData ExtractJobData(IJob job, LabelType labelType)
        {
            if (job == null)
                throw new ArgumentNullException(nameof(job));

            var data = new FurtherProcessingLabelData
            {
                LabelType = labelType
            };

            try
            {
                // Extract basic information
                ExtractBasicInformation(job, data);

                // Extract processing information based on label type
                switch (labelType)
                {
                    case LabelType.FurtherProcessing:
                        ExtractFurtherProcessingData(job, data);
                        break;
                    case LabelType.DPCProcessing:
                        ExtractDPCProcessingData(job, data);
                        break;
                    case LabelType.WideFormatProcessing:
                        ExtractWideFormatProcessingData(job, data);
                        break;
                    case LabelType.FurtherProcessingList:
                        ExtractFurtherProcessingListData(job, data);
                        break;
                    default:
                        ExtractFurtherProcessingData(job, data); // Default to standard further processing
                        break;
                }

                // Extract special instructions
                ExtractSpecialInstructions(job, data);

                // Format all text outputs
                FormatTextOutputs(data);

                return data;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error extracting job data for job {JobId}", job.Id);
                throw;
            }
        }

        private static void ExtractBasicInformation(IJob job, FurtherProcessingLabelData data)
        {
            data.JobName = job.Name;
            data.OrderNumber = job.Order.OrderNr;
            data.JobNumber = job.JobNr;
            data.OriginalJobNumber = job.ReOrderSourceJobId != 0 ? job.ReOrderSourceJobId.ToString() : null;
            data.Courier = job.HasSplitDelivery ? "SPLIT Delivery" : job.Order.Courier;
            data.IsMultiJobOrder = job.Order.Jobs.Count() > 1;
            data.Barcode = job.Barcode;
        }

        private static void ExtractCommonProcessingData(IJob job, FurtherProcessingLabelData data)
        {
            data.Size = job.GetJobSize();
            data.Quantity = job.Quantity;
            data.Stock = job.FinalStock?.Name;
            data.SendSamples = job.SendSamples == true;

            // Run information
            if (job.Runs.Count > 0)
            {
                data.RunNumber = job.Runs[0].RunNr;
            }

            // Hole drilling
            if (job.HoleDrilling != HoleDrilling.None)
            {
                data.HoleDrilling = job.HoleDrilling.ToDescription();
                data.NumberOfHoles = job.NumberOfHoles;
            }

            // Round corners
            if (job.RoundOption != RoundOption.None)
            {
                data.RoundOption = job.RoundOption.ToDescription();
                if (job.RoundOption == RoundOption.Custom)
                {
                    data.CustomDieCut = job.CustomDieCut;
                }
                else if (job.RoundDetailOption != RoundDetailOption.None)
                {
                    data.RoundDetailOption = job.RoundDetailOption.ToDescription();
                }
            }

            // Die cutting
            if (job.DieCutType != CutOptions.None)
            {
                data.DieCutType = job.DieCutType.ToDescription();
            }

            // Folding/Scoring
            if (job.FoldedSize != null)
            {
                data.FoldedSize = $"{job.FoldedSize.Height} x {job.FoldedSize.Width}";
                data.FoldingTitle = job.IsBusinessCard() ? "Scoring" : "Folding";
            }

            // Magnets
            data.NumberOfMagnets = job.NumberOfMagnets;
        }

        private static void ExtractFurtherProcessingData(IJob job, FurtherProcessingLabelData data)
        {
            data.Title = "Further Processing";
            ExtractCommonProcessingData(job, data);
        }

        private static void ExtractDPCProcessingData(IJob job, FurtherProcessingLabelData data)
        {
            data.Title = "DPC Processing";
            ExtractCommonProcessingData(job, data);
            
            data.FrontPrinting = job.FrontPrinting.ToDescription();
            data.BackPrinting = job.BackPrinting.ToDescription();
            data.Scoring = job.Scoring;
            data.Perforating = job.Perforating;
            data.ShowPrintedCelloPacked = true;

            // Celloglaze handling
            if (job.FinalFrontCelloglaze == JobCelloglazeOptions.Gloss && 
                job.FinalBackCelloglaze == JobCelloglazeOptions.Matt)
            {
                data.Celloglaze = "GlossFr/MattBk";
            }
            else
            {
                data.Celloglaze = job.Celloglaze.ToString();
            }

            // Magazine specific
            if (job.IsMagazine())
            {
                data.CoverStock = job.StockForCover?.Name;
                data.Pages = job.Pages;
                
                var binding = job.BindingOption?.Name ?? "";
                if (job.BoundEdge != JobBoundEdgeOptions.None)
                {
                    binding += " " + job.BoundEdge.ToDescription().Substring(0, 1);
                }
                data.BindingOption = binding;
            }

            // Pad direction
            if (job.PadDirection != PadDirection.None)
            {
                data.PadDirection = job.PadDirection.ToDescription();
            }

            // Wiro magazine info
            if (job.IsWiroMagazine() && job.WiroInfo != null)
            {
                data.WiroInfo = FormatWiroInfo(job.WiroInfo);
            }
        }

        private static void ExtractWideFormatProcessingData(IJob job, FurtherProcessingLabelData data)
        {
            data.Title = "Wide Format Processing";
            ExtractCommonProcessingData(job, data);
            
            data.FrontPrinting = job.FrontPrinting.ToDescription();
            data.BackPrinting = job.BackPrinting.ToDescription();
            data.ShowPrintedCelloPacked = true;

            if (job.Pages > 0)
            {
                data.Pages = job.Pages;
            }
        }

        private static void ExtractFurtherProcessingListData(IJob job, FurtherProcessingLabelData data)
        {
            data.Title = "Further Processing List";
            ExtractCommonProcessingData(job, data);
            
            // Add freight packages for FG facility
            if (job.Facility == Facility.FG)
            {
                data.FreightPackages = job.Freight?.Packages?.ToString() ?? "";
            }
        }

        private static void ExtractSpecialInstructions(IJob job, FurtherProcessingLabelData data)
        {
            data.CustomerLogoRequired = job.Order.CustomerLogoRequired;
            data.SpecialInstructions = job.SpecialInstructions;
            data.DigitalJobMailHouseInstructions = job.GetDigitalJobMailHouseInstuctions();
            data.PackWithoutPallets = job.Order.PackWithoutPallets;
        }

        private static string FormatWiroInfo(dynamic wiroInfo)
        {
            try
            {
                return $@"Wiro Info:
Covers
    Outer Front: {wiroInfo.OuterFront ?? "None"}
    Outer Back:  {wiroInfo.OuterBack ?? "None"}

    Inner Front
    Stock: {wiroInfo.InnerFrontStockForCover?.Name ?? "None"}
    Cello: {wiroInfo.InnerFrontCello ?? "None"}

    Inner Back
    Stock: {wiroInfo.InnerBackStockForCover?.Name ?? "None"}
    Cello: {wiroInfo.InnerBackCello ?? "None"}";
            }
            catch
            {
                return "Wiro Info: Error formatting";
            }
        }

        private static void FormatTextOutputs(FurtherProcessingLabelData data)
        {
            // Format basic information
            var basicInfo = new StringBuilder();
            basicInfo.AppendFormat(fmt, "Job Name", data.JobName).AppendLine();
            basicInfo.AppendFormat(fmt, "Order #", data.OrderNumber).AppendLine();
            basicInfo.AppendFormat(fmt, "Job # ", data.JobNumber).AppendLine();

            if (!string.IsNullOrEmpty(data.OriginalJobNumber))
            {
                basicInfo.AppendFormat(fmt, "Orig Job #", data.OriginalJobNumber).AppendLine();
            }

            basicInfo.AppendFormat(fmt, "Courier", data.Courier).AppendLine();

            if (data.IsMultiJobOrder)
            {
                basicInfo.Append("*** Multi Job Order ***");
            }

            data.BasicInformationText = basicInfo.ToString();

            // Format processing text based on label type
            FormatProcessingText(data);

            // Format special instructions
            FormatSpecialInstructionsText(data);
        }

        private static void FormatProcessingText(FurtherProcessingLabelData data)
        {
            var processingText1 = new StringBuilder();
            var processingText2 = new StringBuilder();

            switch (data.LabelType)
            {
                case LabelType.DPCProcessing:
                    FormatDPCProcessingText(data, processingText1, processingText2);
                    break;
                case LabelType.WideFormatProcessing:
                    FormatWideFormatProcessingText(data, processingText1, processingText2);
                    break;
                case LabelType.FurtherProcessingList:
                    FormatFurtherProcessingListText(data, processingText1);
                    break;
                default: // FurtherProcessing
                    FormatStandardFurtherProcessingText(data, processingText1);
                    break;
            }

            data.ProcessingText1 = processingText1.ToString();
            data.ProcessingText2 = processingText2.ToString();
        }

        private static void FormatStandardFurtherProcessingText(FurtherProcessingLabelData data, StringBuilder processingText)
        {
            if (data.SendSamples)
            {
                processingText.AppendLine("*** Send Samples ***");
            }

            if (!string.IsNullOrEmpty(data.RunNumber))
            {
                processingText.AppendFormat(fmt, "Run #", data.RunNumber).AppendLine();
            }

            processingText.AppendFormat(fmt, "Size", data.Size).AppendLine();

            if (!string.IsNullOrEmpty(data.HoleDrilling))
            {
                processingText.AppendFormat(fmt, "Hole Drilling", data.HoleDrilling).AppendLine();
                if (data.NumberOfHoles.HasValue && data.NumberOfHoles > 0)
                {
                    processingText.AppendFormat(fmt, "# Of Holes", data.NumberOfHoles).AppendLine();
                }
            }

            if (!string.IsNullOrEmpty(data.RoundOption))
            {
                processingText.AppendFormat(fmt, "Round Corner", data.RoundOption).AppendLine();
                if (!string.IsNullOrEmpty(data.CustomDieCut))
                {
                    processingText.AppendLine(data.CustomDieCut);
                }
                else if (!string.IsNullOrEmpty(data.RoundDetailOption))
                {
                    processingText.AppendLine(data.RoundDetailOption);
                }
            }

            if (!string.IsNullOrEmpty(data.DieCutType))
            {
                processingText.AppendFormat(fmt, "Diecut", data.DieCutType).AppendLine();
            }

            if (!string.IsNullOrEmpty(data.FoldedSize))
            {
                processingText.AppendFormat(fmt, data.FoldingTitle, data.FoldedSize).AppendLine();
            }

            if (data.NumberOfMagnets > 0)
            {
                processingText.AppendFormat(fmt, "Magnet", data.NumberOfMagnets).AppendLine();
            }
        }

        private static void FormatDPCProcessingText(FurtherProcessingLabelData data, StringBuilder processingText1, StringBuilder processingText2)
        {
            processingText1.AppendFormat(fmt, "Job # ", data.JobNumber).AppendLine();
            processingText1.AppendFormat(fmt, "Stock", data.Stock).AppendLine();

            // Magazine specific
            if (!string.IsNullOrEmpty(data.CoverStock))
            {
                processingText1.AppendFormat(fmt, "Cover stock:", data.CoverStock).AppendLine();
            }
            if (data.Pages > 0)
            {
                processingText1.AppendFormat(fmt, "# of pages:", data.Pages).AppendLine();
            }
            if (!string.IsNullOrEmpty(data.BindingOption))
            {
                processingText1.AppendFormat(fmt, "Binding: ", data.BindingOption).AppendLine();
            }

            processingText1.AppendFormat(fmt, "Size", data.Size).AppendLine();
            processingText1.AppendFormat(fmt, "Quantity", data.Quantity).AppendLine();

            if (data.Scoring)
            {
                processingText1.AppendFormat(fmt, "Scoring", "Yes").AppendLine();
            }

            if (data.Perforating)
            {
                processingText1.AppendFormat(fmt, "Perforation", "Yes").AppendLine();
            }

            if (!string.IsNullOrEmpty(data.FoldedSize))
            {
                processingText1.AppendFormat(fmt, data.FoldingTitle, data.FoldedSize).AppendLine();
            }

            if (!string.IsNullOrEmpty(data.Celloglaze))
            {
                processingText1.AppendFormat(fmt, "Cello", data.Celloglaze).AppendLine();
            }

            if (!string.IsNullOrEmpty(data.DieCutType))
            {
                processingText1.AppendFormat(fmt, "Diecut", data.DieCutType).AppendLine();
            }

            if (data.NumberOfMagnets > 0)
            {
                processingText1.AppendFormat(fmt, "Magnet", data.NumberOfMagnets).AppendLine();
            }

            if (!string.IsNullOrEmpty(data.HoleDrilling))
            {
                processingText1.AppendFormat(fmt, "Hole Drilling", data.HoleDrilling).AppendLine();
                if (data.NumberOfHoles.HasValue && data.NumberOfHoles > 0)
                {
                    processingText1.AppendFormat(fmt, "# Of Holes", data.NumberOfHoles).AppendLine();
                }
            }

            if (!string.IsNullOrEmpty(data.RoundOption))
            {
                processingText1.AppendFormat(fmt, "Round Corner", data.RoundOption).AppendLine();
                if (!string.IsNullOrEmpty(data.CustomDieCut))
                {
                    processingText1.AppendLine(data.CustomDieCut);
                }
                else if (!string.IsNullOrEmpty(data.RoundDetailOption))
                {
                    processingText1.AppendLine(data.RoundDetailOption);
                }
            }

            if (!string.IsNullOrEmpty(data.PadDirection))
            {
                processingText1.AppendFormat(fmt, "Finish by", data.PadDirection).AppendLine();
            }

            if (data.ShowPrintedCelloPacked)
            {
                processingText1.AppendLine("Printed____\nCello____\nPacked____ ");
            }
        }

        private static void FormatWideFormatProcessingText(FurtherProcessingLabelData data, StringBuilder processingText1, StringBuilder processingText2)
        {
            processingText1.AppendFormat(fmt, "Job   #", data.JobNumber).AppendLine();
            processingText1.AppendFormat(fmt, "Stock", data.Stock).AppendLine();
            processingText1.AppendFormat(fmt, "Courier", data.Courier).AppendLine();
            processingText1.AppendFormat(fmt, "Size", data.Size).AppendLine();
            processingText1.AppendFormat(fmt, "Front", data.FrontPrinting).AppendLine();
            processingText1.AppendFormat(fmt, "Back", data.BackPrinting).AppendLine();

            if (data.Pages > 0)
            {
                processingText1.AppendFormat(fmt, "Sheet/pad", data.Pages).AppendLine();
            }

            processingText1.AppendFormat(fmt, "Quantity", data.Quantity).AppendLine();

            if (data.CustomerLogoRequired)
            {
                processingText1.Append("*** Logo label required ***").AppendLine();
            }

            if (data.SendSamples)
            {
                processingText1.Append("*** Send samples ***").AppendLine();
            }

            if (!string.IsNullOrEmpty(data.FoldedSize))
            {
                processingText1.AppendFormat(fmt, "Fold", data.FoldedSize).AppendLine();
                processingText1.AppendFormat(fmt, data.FoldingTitle, data.FoldedSize).AppendLine();
            }

            if (!string.IsNullOrEmpty(data.HoleDrilling))
            {
                processingText1.AppendFormat(fmt, "Hole Drilling", data.HoleDrilling).AppendLine();
                if (data.NumberOfHoles.HasValue && data.NumberOfHoles > 0)
                {
                    processingText1.AppendFormat(fmt, "# Of Holes", data.NumberOfHoles).AppendLine();
                }
            }

            if (!string.IsNullOrEmpty(data.RoundOption))
            {
                processingText1.AppendFormat(fmt, "Round Corner", data.RoundOption).AppendLine();
                if (!string.IsNullOrEmpty(data.CustomDieCut))
                {
                    processingText1.AppendLine(data.CustomDieCut);
                }
                else if (!string.IsNullOrEmpty(data.RoundDetailOption))
                {
                    processingText1.AppendLine(data.RoundDetailOption);
                }
            }

            if (data.ShowPrintedCelloPacked)
            {
                processingText1.AppendLine("Printed____\nCello____\nPacked____ ");
            }
        }

        private static void FormatFurtherProcessingListText(FurtherProcessingLabelData data, StringBuilder processingText)
        {
            processingText.AppendFormat(fmt, "Courier", data.Courier).AppendLine();

            if (data.SendSamples)
            {
                processingText.AppendLine("*** Send Samples ***");
            }

            if (!string.IsNullOrEmpty(data.RunNumber))
            {
                processingText.AppendFormat(fmt, "Run #", data.RunNumber).AppendLine();
            }

            processingText.AppendFormat(fmt, "Size", data.Size).AppendLine();

            if (!string.IsNullOrEmpty(data.HoleDrilling))
            {
                processingText.AppendFormat(fmt, "Hole Drilling ", data.HoleDrilling).AppendLine();
                if (data.NumberOfHoles.HasValue && data.NumberOfHoles > 0)
                {
                    processingText.AppendFormat(fmt, "Number Of Holes", data.NumberOfHoles).AppendLine();
                }
            }

            if (!string.IsNullOrEmpty(data.RoundOption))
            {
                processingText.AppendFormat(fmt, "Round Corner", data.RoundOption).AppendLine();
                if (!string.IsNullOrEmpty(data.CustomDieCut))
                {
                    processingText.AppendLine(data.CustomDieCut);
                }
                else if (!string.IsNullOrEmpty(data.RoundDetailOption))
                {
                    processingText.AppendLine(data.RoundDetailOption);
                }
            }

            if (!string.IsNullOrEmpty(data.DieCutType))
            {
                processingText.AppendFormat(fmt, "Diecut", data.DieCutType).AppendLine();
            }

            if (!string.IsNullOrEmpty(data.FoldedSize))
            {
                processingText.AppendFormat(fmt, data.FoldingTitle, data.FoldedSize).AppendLine();
            }

            if (data.NumberOfMagnets > 0)
            {
                processingText.AppendFormat(fmt, "Magnet", data.NumberOfMagnets).AppendLine();
            }
        }

        private static void FormatSpecialInstructionsText(FurtherProcessingLabelData data)
        {
            var instructions = new StringBuilder();

            if (data.CustomerLogoRequired)
            {
                instructions.Append("*** Logo label required ***\n");
            }

            if (data.SendSamples)
            {
                instructions.Append("*** Send samples ***\n");
            }

            if (!string.IsNullOrEmpty(data.SpecialInstructions))
            {
                instructions.Append(data.SpecialInstructions).Append("\n");
            }

            if (!string.IsNullOrEmpty(data.WiroInfo))
            {
                instructions.Append(data.WiroInfo).Append("\n");
            }

            if (!string.IsNullOrEmpty(data.DigitalJobMailHouseInstructions))
            {
                instructions.Append(data.DigitalJobMailHouseInstructions);
            }

            if (data.PackWithoutPallets)
            {
                instructions.Append("\n***Pack Loose Cartons***");
            }

            if (!string.IsNullOrEmpty(data.FreightPackages))
            {
                instructions.Append(data.FreightPackages);
            }

            data.SpecialInstructionsText = instructions.ToString();
        }

        /// <summary>
        /// Helper method to get formatted text for a specific section
        /// </summary>
        /// <param name="job">The job to extract information from</param>
        /// <param name="labelType">The type of label being generated</param>
        /// <param name="section">The section to get (BasicInformation, ProcessingText1, ProcessingText2, SpecialInstructions)</param>
        /// <returns>Formatted text for the specified section</returns>
        public static string GetFormattedSection(IJob job, LabelType labelType, string section)
        {
            var data = ExtractJobData(job, labelType);

            return section.ToLower() switch
            {
                "basicinformation" => data.BasicInformationText,
                "processingtext1" => data.ProcessingText1,
                "processingtext2" => data.ProcessingText2,
                "specialinstructions" => data.SpecialInstructionsText,
                _ => throw new ArgumentException($"Unknown section: {section}")
            };
        }
    }

    /// <summary>
    /// Enum to specify label types for data extraction
    /// </summary>
    public enum LabelType
    {
        FurtherProcessing,
        DPCProcessing,
        WideFormatProcessing,
        FurtherProcessingList
    }
}
