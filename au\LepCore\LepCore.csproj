﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <!-- -->
  <Target Name="DisableAnalyzers" BeforeTargets="CoreCompile" Condition="'$(UseRoslynAnalyzers)' == 'false'">
    <ItemGroup>
      <Analyzer Remove="@(Analyzer)" />
    </ItemGroup>
  </Target>

    <PropertyGroup>



        <!--  -->
        <IsPublishable>True</IsPublishable>
        <DefineConstants>$(DefineConstants);</DefineConstants>
        <AssemblyName>LepCore</AssemblyName>
        <OutputType>Exe</OutputType>
        <DebugType>portable</DebugType>
        <PackageId>LepCore</PackageId>
        <UserSecretsId>aspnet-LepCore-220b68f4-13d4-44d6-958b-a689cdfe4083</UserSecretsId>
        <PreserveCompilationContext>false</PreserveCompilationContext>
        <MvcRazorCompileOnPublish>false</MvcRazorCompileOnPublish>
        <SatelliteResourceLanguages>en</SatelliteResourceLanguages>

        <SpaRoot>FrontEnd\</SpaRoot>
        <DefaultItemExcludes>$(DefaultItemExcludes);wwwroot;dist;node_modules\**\*;bower_components\**\*;package-lock.json;*.lock.json;server.js;e2e\**\*;logs\**\*;fonts\**\*;devServer\**\*;fontsUnused\**\*;\.gitignore;\bower.json;e2e.js</DefaultItemExcludes>
        <Configurations>Debug;Release</Configurations>
        <LangVersion>latest</LangVersion>
        <AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
        <EnableNETAnalyzers>False</EnableNETAnalyzers>
        <TargetFramework>net8.0-windows7.0</TargetFramework>
        <!--
        <RunAnalyzersDuringBuild>False</RunAnalyzersDuringBuild>
        <RunAnalyzersDuringLiveAnalysis>False</RunAnalyzersDuringLiveAnalysis>
         -->
    </PropertyGroup>


    <ItemGroup>
        <!-- Don't publish the SPA source files, but do show them in the project files list -->
        <Compile Remove="wwwroot\**" />
        <Content Remove="$(SpaRoot)**" />
        <Content Remove="wwwroot\**" />
        <EmbeddedResource Remove="wwwroot\**" />
        <None Remove="$(SpaRoot)**" />
        <None Remove="wwwroot\**" />
        <None Include="$(SpaRoot)**" Exclude="$(SpaRoot)node_modules\**;$(SpaRoot)bower_components\**;$(SpaRoot)devServer\**;$(SpaRoot)e2e\**;$(SpaRoot)fonts*\**;" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\LepCore.DTOs\LepCore.DTOs.csproj" />
        <ProjectReference Include="..\code\main\main.csproj" />
    </ItemGroup>


    <ItemGroup>
        <!--<PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="6.0.0" />-->
        <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
        <PackageReference Include="FastReport.Compat" Version="2023.1.0" />
        <PackageReference Include="FastReport.OpenSource" Version="2023.1.1" />
        <PackageReference Include="FastReport.OpenSource.Data.Json" Version="2021.4.0" />
        <PackageReference Include="FastReport.OpenSource.Export.PdfSimple" Version="2023.1.1" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.6" />
        <PackageReference Include="AutoMapper" Version="12.0.0" />
        <PackageReference Include="CoreWCF.Http" Version="1.0.2" />
        <PackageReference Include="CoreWCF.NetTcp" Version="1.0.2" />
        <PackageReference Include="CsvHelper" Version="32.0.3" />
        <PackageReference Include="JsonDiffPatch.Net" Version="2.1.0" />
        <PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="7.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.0" />
        <PackageReference Include="serilog-enricher-middleware" Version="1.2.1" />
        <PackageReference Include="Serilog.AspNetCore" Version="3.2.0" />
        <PackageReference Include="Serilog.Enrichers.AspNetCore" Version="1.0.0" />
        <PackageReference Include="Serilog.Enrichers.AspnetcoreHttpcontext" Version="1.1.0" />
        <PackageReference Include="Serilog.Enrichers.CorrelationId" Version="3.0.1" />
        <PackageReference Include="Serilog.Extensions.Logging" Version="3.0.1" />
        <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
        <PackageReference Include="Serilog.Sinks.Seq" Version="4.0.0" />


        <PackageReference Include="Stripe.net" Version="28.10.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.3.1" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.0" />
        <PackageReference Include="System.Runtime" Version="4.3.1" />
        <PackageReference Include="System.ServiceModel.Http" Version="4.10.0" />
        <PackageReference Include="SystemTextJson.JsonDiffPatch" Version="1.3.0" />
    </ItemGroup>



    <ItemGroup>
        <Content Update="appsettings.*.json">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Content>
    </ItemGroup>


    <!-- Build SPA during development builds -->
    <!-- <Target Name="BuildSpa" AfterTargets="Build" Condition="'$(Configuration)' == 'Debug'">
        <Message Importance="high" Text="Building SPA for development..." />
        <Exec WorkingDirectory="$(SpaRoot)" Command="gulp build" ContinueOnError="true" />
    </Target> -->


    <ItemGroup>
        <Watch Include="..\**\*.cs" />
    </ItemGroup>


    <ItemGroup>
      <Watch Remove="wwwroot\**" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="appsettings.DESKTOP-R1OTS8F.json" />
      <Content Remove="appsettings.henry.json" />
      <Content Remove="appsettings.icemedia.json" />
      <Content Remove="appsettings.json" />
      <Content Remove="appsettings.now.json" />
      <Content Remove="appsettings.pc11452.json" />
      <Content Remove="appsettings.pc11532.json" />
      <Content Remove="appsettings.now.json" />
      <Content Remove="web.config" />
    </ItemGroup>

    <ItemGroup>
      <None Include="appsettings.DESKTOP-R1OTS8F.json">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </None>
      <None Include="appsettings.henry.json">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </None>
      <None Include="appsettings.icemedia.json">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </None>
      <None Include="appsettings.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Include="appsettings.now.json" />
      <None Include="appsettings.pc11452.json">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </None>
      <None Include="appsettings.pc11532.json">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </None>
      <None Include="appsettings.now.json">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <Content Update="appsettings.app07-live.json">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </Content>
      <Content Update="appsettings.app07.json">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>

      <None Update="appsettings.henry.json.bak">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </None>

      <None Update="lepcore.pfx">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </None>

      <None Update="Properties\launchSettings.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>


</Project>