# ============================================================================
# LepCore Publish to c:\lepsf\upd (PowerShell Version)
# ============================================================================
# This PowerShell script publishes LepCore using the FolderProfile publish profile
# Target: c:\lepsf\upd
# Configuration: Release
# Platform: x64
# ============================================================================

param(
    [switch]$Force,
    [switch]$Verbose,
    [switch]$SkipBuild
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Function to write section headers
function Write-Section {
    param([string]$Title)
    Write-Host ""
    Write-Host "============================================================================" -ForegroundColor Cyan
    Write-Host " $Title" -ForegroundColor Cyan
    Write-Host "============================================================================" -ForegroundColor Cyan
    Write-Host ""
}

try {
    Write-Section "LepCore Publish to c:\lepsf\upd"

    # Get script directory and set working directory
    $ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    Set-Location $ScriptDir
    
    Write-ColorOutput "Current directory: $(Get-Location)" -Color Green
    Write-ColorOutput "Script directory: $ScriptDir" -Color Green

    # Check if project file exists
    $ProjectFile = "LepCore.csproj"
    if (-not (Test-Path $ProjectFile)) {
        throw "ERROR: $ProjectFile not found in current directory: $(Get-Location)"
    }

    Write-ColorOutput "Project file: $ProjectFile" -Color Green
    Write-ColorOutput "Publish profile: FolderProfile" -Color Green
    Write-ColorOutput "Target directory: c:\lepsf\upd" -Color Green

    # Create target directory if it doesn't exist
    $TargetDir = "c:\lepsf\upd"
    if (-not (Test-Path $TargetDir)) {
        Write-ColorOutput "Creating target directory: $TargetDir" -Color Yellow
        New-Item -ItemType Directory -Path $TargetDir -Force | Out-Null
    }

    # Check if target directory has files and warn user
    if ((Get-ChildItem $TargetDir -ErrorAction SilentlyContinue).Count -gt 0 -and -not $Force) {
        Write-ColorOutput "WARNING: Target directory contains files that will be deleted!" -Color Red
        Write-ColorOutput "Files in $TargetDir :" -Color Yellow
        Get-ChildItem $TargetDir | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor Yellow }
        Write-Host ""
        $response = Read-Host "Continue? (y/N)"
        if ($response -ne "y" -and $response -ne "Y") {
            Write-ColorOutput "Publish cancelled by user." -Color Yellow
            exit 0
        }
    }

    Write-Section "Starting Publish Process"

    # Build the dotnet publish command
    # Note: Using --output instead of PublishProfile for more reliable targeting
    $PublishArgs = @(
        "publish"
        $ProjectFile
        "--configuration", "Release"
        "--framework", "net8.0-windows7.0"
        "--runtime", "win-x64"
        "--self-contained", "false"
        "--output", $TargetDir
    )

    if ($Verbose) {
        $PublishArgs += "--verbosity", "detailed"
    } else {
        $PublishArgs += "--verbosity", "normal"
    }

    if ($SkipBuild) {
        $PublishArgs += "--no-build"
    }

    Write-ColorOutput "Running command: dotnet $($PublishArgs -join ' ')" -Color Cyan
    Write-Host ""

    # Execute the publish command
    $StartTime = Get-Date
    & dotnet $PublishArgs

    if ($LASTEXITCODE -eq 0) {
        $EndTime = Get-Date
        $Duration = $EndTime - $StartTime

        Write-Section "PUBLISH SUCCESSFUL!"
        
        Write-ColorOutput "Published to: $TargetDir" -Color Green
        Write-ColorOutput "Configuration: Release" -Color Green
        Write-ColorOutput "Framework: net8.0-windows7.0" -Color Green
        Write-ColorOutput "Runtime: win-x64" -Color Green
        Write-ColorOutput "Duration: $($Duration.TotalSeconds.ToString('F2')) seconds" -Color Green
        Write-Host ""

        Write-ColorOutput "Files in target directory:" -Color Green
        try {
            $files = Get-ChildItem $TargetDir -ErrorAction SilentlyContinue
            if ($files) {
                $files | ForEach-Object {
                    Write-Host "  - $($_.Name)" -ForegroundColor White
                }
            } else {
                Write-ColorOutput "  (No files found or directory access issue)" -Color Yellow
            }
        } catch {
            Write-ColorOutput "  (Error listing files: $($_.Exception.Message))" -Color Yellow
        }
        Write-Host ""

        Write-ColorOutput "Publish completed successfully at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -Color Green

    } else {
        throw "Publish failed with exit code: $LASTEXITCODE"
    }

} catch {
    Write-Section "PUBLISH FAILED!"
    
    Write-ColorOutput "Error: $($_.Exception.Message)" -Color Red
    Write-Host ""
    
    Write-ColorOutput "Common issues:" -Color Yellow
    Write-ColorOutput "- Build errors in the project" -Color Yellow
    Write-ColorOutput "- Missing dependencies" -Color Yellow
    Write-ColorOutput "- Insufficient permissions to write to c:\lepsf\upd" -Color Yellow
    Write-ColorOutput "- Network issues if accessing remote resources" -Color Yellow
    Write-ColorOutput "- Frontend build issues (gulp/npm)" -Color Yellow
    Write-Host ""
    
    Write-ColorOutput "Try running with -Verbose for more detailed output" -Color Cyan
    
    exit 1
}

Write-Host ""
Write-ColorOutput "Press any key to exit..." -Color Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
