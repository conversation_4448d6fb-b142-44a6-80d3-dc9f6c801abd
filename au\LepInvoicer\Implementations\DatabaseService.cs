using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Data.SqlClient;
using NHibernate;
using NHibernate.Linq;
using LepInvoicer.Implementations;
using LepInvoicer.Interfaces;

namespace LepInvoicer.Implementations;

/// <summary>
/// Database service implementation for LEP Invoicer operations
/// </summary>
public class DatabaseService : IDatabaseService
{
    private readonly ILogger<DatabaseService> _logger;
    private readonly InvoicerConfiguration _config;
    private Microsoft.Data.SqlClient.SqlConnection _connection;
    private ISession _session;
    private bool _disposed = false;

    public DatabaseService(ILogger<DatabaseService> logger, IOptions<InvoicerConfiguration> config, ISession session)
    {
        _logger = logger;
        _config = config.Value;
        _session = session;
    }

    public Task Initialize()
    {
        try
        {
            _logger.LogInformation("Initializing database connection and NHibernate session...");

            // Initialize SQL connection
            _connection = new Microsoft.Data.SqlClient.SqlConnection(_config.ConnectionString);
            _connection.Open();

            // Execute initial cleanup SQL
            ExecuteSql(InvoicerConstants.SqlQueries.CleanupInitialSql).Wait();

            _logger.LogInformation("Database connection established successfully");
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize database connection");
            throw;
        }
    }

    public async Task<List<KeyValuePair<int, string>>> GetOrdersToInvoice(int batchSize)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        _logger.LogInformation("Getting {BatchSize} orders to invoice", batchSize);

        try
        {
            // Use HQL to avoid the problematic WLCustomerName formula property
            var hql = @"
                SELECT o.Id, o.FinishDate, o.Invoiced2, c.Name, c.Username
                FROM IOrder o
                JOIN o.Customer c
                WHERE c.Name NOT IN (:ignoreCustomers)
                    AND (o.Invoiced2 IS NULL OR o.Invoiced2 NOT IN ('Y', 'F', 'C'))
                    AND o.FinishDate IS NOT NULL
                    AND YEAR(o.FinishDate) != 1
                    AND o.FinishDate >= :minDate
                ORDER BY o.Id DESC";

            var candidateOrderData = _session.CreateQuery(hql)
                .SetParameterList("ignoreCustomers", _config.IgnoreCustomers)
                .SetParameter("minDate", _config.MinimumFinishDate)
                .SetMaxResults(batchSize * 3)
                .List<object[]>()
                .Select(row => new {
                    Id = Convert.ToInt32(row[0]),
                    FinishDate = row[1] as DateTime?,
                    Invoiced2 = row[2] as string,
                    CustomerName = row[3] as string,
                    CustomerUsername = row[4] as string
                })
                .ToList();

            // Now get the full order entities for the selected IDs to access PriceOfJobs
            var orderIds = candidateOrderData.Select(o => o.Id).ToList();
            var candidateOrders = _session.Query<IOrder>()
                .Where(o => orderIds.Contains(o.Id))
                .ToList();

            // Process zero-priced orders first (matching LinqPad script behavior)
            var zeroPricedOrders = candidateOrders
                .Where(o => !o.PriceOfJobs.HasValue || o.PriceOfJobs.Value == 0)
                .ToList();

            if (zeroPricedOrders.Any())
            {
                _logger.LogInformation("Marking {Count} zero-priced orders as invoiced", zeroPricedOrders.Count);
                foreach (var order in zeroPricedOrders)
                {
                    await MarkOrderInvoiced(order.Id);
                }
            }

            // Filter out zero-priced orders and return valid ones
            var validOrders = candidateOrders
                .Where(o => o.PriceOfJobs.HasValue && o.PriceOfJobs.Value > 0)
                .Take(batchSize)
                .Select(o => new KeyValuePair<int, string>(o.Id, o.Customer.Username))
                .ToList();

            _logger.LogInformation("Found {OrderCount} orders to invoice (marked {ZeroCount} zero-priced as invoiced, filtered {CandidateCount} candidates)",
                validOrders.Count, zeroPricedOrders.Count, candidateOrders.Count);
            return validOrders;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get orders to invoice");
            throw;
        }
    }

    public Task<List<OrderCredit>> GetCreditsToInvoice(int batchSize)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        _logger.LogInformation("Getting {BatchSize} credits to invoice", batchSize);

        try
        {
            // Use raw SQL to properly handle the varchar Invoiced field
            var sql = $@"
                SELECT TOP ({batchSize}) oc.*
                FROM [OrderCredit] oc
                INNER JOIN [Order] o ON oc.OrderId = o.Id
                INNER JOIN [Customer] cu ON o.userId = cu.Id
                WHERE (oc.Invoiced IS NULL OR oc.Invoiced = 'N')
                    AND oc.Type IN ('C', 'M', 'CI')
                    AND oc.OrderId IS NOT NULL
                    AND cu.Name NOT IN ('" + string.Join("', '", _config.IgnoreCustomers) + @"')
                ORDER BY oc.DateCreated";

            var query = _session.CreateSQLQuery(sql)
                .AddEntity(typeof(OrderCredit));

            var credits = query.List<OrderCredit>().ToList();

            _logger.LogInformation("Found {CreditCount} credits to invoice", credits.Count);
            return Task.FromResult(credits);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get credits to invoice");
            throw;
        }
    }

    public Task<List<OrderCredit>> GetRefundsToInvoice(int batchSize)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        _logger.LogInformation("Getting {BatchSize} refunds to invoice", batchSize);

        try
        {
            // Use raw SQL to properly handle the varchar Invoiced field
            var sql = $@"
                SELECT TOP ({batchSize}) oc.*
                FROM [OrderCredit] oc
                INNER JOIN [Customer] cu ON oc.CustomerId = cu.CustomerId
                WHERE (oc.Invoiced IS NULL OR oc.Invoiced = 'N')
                    AND oc.Type = 'S'
                    AND oc.CustomerId IS NOT NULL
                    AND cu.Name NOT IN ('" + string.Join("', '", _config.IgnoreCustomers) + @"')
                ORDER BY oc.DateCreated";

            var query = _session.CreateSQLQuery(sql)
                .AddEntity(typeof(OrderCredit));

            var refunds = query.List<OrderCredit>().ToList();

            _logger.LogInformation("Found {RefundCount} refunds to invoice", refunds.Count);
            return Task.FromResult(refunds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get refunds to invoice");
            throw;
        }
    }

    public Task<IOrder> GetOrder(int orderId)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        _logger.LogInformation("Getting order {OrderId}", orderId);

        try
        {
            var order = _session.Query<IOrder>()
                .Fetch(x => x.Jobs)
                .Where(x => x.Id == orderId)
                .FirstOrDefault();

            if (order == null)
                throw new InvalidOperationException($"Order {orderId} not found");

            return Task.FromResult(order);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get order {OrderId}", orderId);
            throw;
        }
    }

    public Task<List<IOrder>> GetInvoicedOrdersAfterDate(DateTime fromDate)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        _logger.LogInformation("Getting invoiced orders after {FromDate}", fromDate.ToString("yyyy-MM-dd"));

        try
        {
            var orders = _session.Query<IOrder>()
                .Fetch(o => o.Customer)
                .Where(o => o.Invoiced2 == "Y") // Successfully invoiced
                .Where(o => o.FinishDate != null && o.FinishDate.Value.Date >= fromDate.Date)
                .Where(o => !_config.IgnoreCustomers.Contains(o.Customer.Name))
                .OrderBy(o => o.Id)
                .ToList();

            _logger.LogInformation("Found {OrderCount} invoiced orders after {FromDate}", orders.Count, fromDate.ToString("yyyy-MM-dd"));
            return Task.FromResult(orders);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get invoiced orders after {FromDate}", fromDate.ToString("yyyy-MM-dd"));
            throw;
        }
    }

    public Task ExecuteSql(string sql)
    {
        if (_connection == null)
            throw new InvalidOperationException("Database not initialized");

        try
        {
            _logger.LogDebug("Executing SQL: {Sql}", sql);
            
            using var command = new Microsoft.Data.SqlClient.SqlCommand(sql, _connection);
            command.ExecuteNonQuery();
            
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute SQL: {Sql}", sql);
            throw;
        }
    }

    public Task MarkOrderInvoiced(int orderId)
    {
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkOrderInvoicedSql, orderId);
        return ExecuteSql(sql);
    }

    public Task MarkOrderFailed(int orderId, string errorMessage)
    {
        var escapedMessage = InvoicerUtilities.EscapeSqlString(errorMessage);
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkOrderFailedSql, orderId, escapedMessage);
        return ExecuteSql(sql);
    }

    public Task MarkCreditInvoiced(int creditId)
    {
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkCreditInvoicedSql, creditId);
        return ExecuteSql(sql);
    }

    public Task MarkCreditFailed(int creditId)
    {
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkCreditFailedSql, creditId);
        return ExecuteSql(sql);
    }

    public Task MarkRefundInvoiced(int refundId)
    {
        // Refunds are stored in the same OrderCredit table, so we use the same SQL as credits
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkCreditInvoicedSql, refundId);
        return ExecuteSql(sql);
    }

    public Task MarkRefundFailed(int refundId)
    {
        // Refunds are stored in the same OrderCredit table, so we use the same SQL as credits
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkCreditFailedSql, refundId);
        return ExecuteSql(sql);
    }



    public Task LogInvoicingResult(int orderId, int jobCount, decimal total, DateTime finishDate, bool success, string details)
    {
        var successFlag = success ? InvoicerConstants.Database.LogSuccessFlag : InvoicerConstants.Database.LogFailureFlag;
        var escapedDetails = details != null ? $"'{InvoicerUtilities.EscapeSqlString(details)}'" : "null";

        // Convert orderId = 0 to NULL for refunds (relaxed constraint now allows this)
        var orderIdValue = orderId == 0 ? "NULL" : orderId.ToString();

        var sql = string.Format(InvoicerConstants.SqlQueries.LogInvoicingResultSql,
            orderIdValue, jobCount, total, finishDate.ToString(_config.DateFormat), successFlag, escapedDetails, DateTime.Now.ToString(_config.DateFormat));

        return ExecuteSql(sql);
    }

    public Task CleanupInvoicerLogs()
    {
        _logger.LogInformation("Cleaning up invoicer logs...");
        return ExecuteSql(InvoicerConstants.SqlQueries.CleanupInvoicerLogsSql);
    }

    public Task<int?> GetSalesConsultantId(string salesConsultantName)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        if (string.IsNullOrEmpty(salesConsultantName))
            return Task.FromResult<int?>(null);

        try
        {
            var sql = "SELECT Id FROM SalesConsultant WHERE Name = :name";
            var result = _session.CreateSQLQuery(sql)
                .SetParameter("name", salesConsultantName)
                .UniqueResult<int?>();

            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get sales consultant ID for {SalesConsultantName}", salesConsultantName);
            return Task.FromResult<int?>(null);
        }
    }

    public Task<Dictionary<string, int>> GetOrderCreditTypeSummary()
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        try
        {
            var summary = _session.Query<OrderCredit>()
                .Where(c => !c.Invoiced)
                .GroupBy(c => c.Type)
                .Select(g => new { Type = g.Key, Count = g.Count() })
                .ToDictionary(x => x.Type ?? "NULL", x => x.Count);

            _logger.LogInformation("OrderCredit Type Summary: {Summary}",
                string.Join(", ", summary.Select(kvp => $"{kvp.Key}: {kvp.Value}")));

            return Task.FromResult(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get OrderCredit type summary");
            throw;
        }
    }

    public async Task CreateInvoicer2LogEntriesForCredits()
    {
        if (_connection == null)
            throw new InvalidOperationException("Database not initialized");

        try
        {
            _logger.LogInformation("=== CREATING INVOICER2LOG ENTRIES FOR CREDITS ===");

            // Get 10 records of each type
            var types = new[] { "C", "M", "CI", "S" };

            foreach (var type in types)
            {
                _logger.LogInformation("--- Processing Type '{Type}' ---", type);

                var sql = $@"
                    SELECT TOP 10
                        oc.Id,
                        oc.Type,
                        ISNULL(oc.Amount, 0) as Amount,
                        oc.DateCreated,
                        oc.OrderId,
                        oc.CustomerId
                    FROM OrderCredit oc
                    WHERE oc.Type = '{type}'
                    ORDER BY oc.DateCreated DESC";

                using var command = new Microsoft.Data.SqlClient.SqlCommand(sql, _connection);
                using var reader = await command.ExecuteReaderAsync();

                var records = new List<(int Id, string Type, decimal Amount, DateTime DateCreated, int? OrderId, int? CustomerId)>();

                while (await reader.ReadAsync())
                {
                    var creditId = reader.GetInt32(0); // Id
                    var amount = reader.GetDecimal(2); // Amount
                    var dateCreated = reader.GetDateTime(3); // DateCreated
                    var orderIdFromCredit = reader.IsDBNull(4) ? (int?)null : reader.GetInt32(4); // OrderId
                    var customerId = reader.IsDBNull(5) ? (int?)null : reader.GetInt32(5); // CustomerId

                    records.Add((creditId, type, amount, dateCreated, orderIdFromCredit, customerId));
                }

                reader.Close();

                // Now create Invoicer2Log entries
                foreach (var record in records)
                {
                    await CreateInvoicer2LogEntryForCredit(record.Id, record.Type, record.Amount, record.DateCreated, record.OrderId, record.CustomerId);
                }

                _logger.LogInformation("  Created {Count} Invoicer2Log entries for type '{Type}'", records.Count, type);
            }

            _logger.LogInformation("=== INVOICER2LOG CREATION COMPLETE ===");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create Invoicer2Log entries for credits");
            throw;
        }
    }

    private async Task CreateInvoicer2LogEntryForCredit(int creditId, string type, decimal amount, DateTime dateCreated, int? orderId, int? customerId)
    {
        try
        {
            // Determine the OrderId to use for logging
            string orderIdValue;
            string logOrderIdDisplay;

            if (orderId.HasValue)
            {
                // Use the associated order ID (for types C, M, CI)
                orderIdValue = orderId.Value.ToString();
                logOrderIdDisplay = orderId.Value.ToString();
            }
            else
            {
                // For refunds (type S) without OrderId, use NULL
                orderIdValue = "NULL";
                logOrderIdDisplay = "NULL";
            }

            var sql = $@"
                INSERT INTO Invoicer2Log ([OrderId], [JobCount], [Total], [FinishDate], [Success], [Details], [DateCreated])
                VALUES ({orderIdValue}, 1, {amount}, '{dateCreated:yyyy-MM-dd HH:mm:ss}', 'Y', 'Credit Type {type} - CreditId {creditId} - CustomerId {customerId}', '{DateTime.Now:yyyy-MM-dd HH:mm:ss}')";

            using var command = new Microsoft.Data.SqlClient.SqlCommand(sql, _connection);
            await command.ExecuteNonQueryAsync();

            _logger.LogInformation("  Created Invoicer2Log entry: CreditId={CreditId}, Type={Type}, Amount={Amount:C}, OrderId={OrderId}, CustomerId={CustomerId}",
                creditId, type, amount, logOrderIdDisplay, customerId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create Invoicer2Log entry for credit {CreditId}", creditId);
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _connection?.Close();
            _connection?.Dispose();
            _disposed = true;
            _logger.LogInformation("Database connection disposed");
        }
    }
}
